{"name": "web", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "fs": "^0.0.1-security", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "multer": "^2.0.1", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "path": "^0.12.7", "pip": "^0.0.1", "socket.io": "^4.8.1"}}