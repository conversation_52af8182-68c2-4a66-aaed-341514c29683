# ✅ FILE: backend/main.py
from fastapi import FastAP<PERSON>, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from model.ml_model import predict_survival
import uvicorn
import logging

#for dashboard
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from sklearn.metrics import accuracy_score
from fastapi.responses import JSONResponse
import os
import joblib
import pandas as pd

app = FastAPI()

# Serve static frontend files from /public
app.mount("/dashboard", StaticFiles(directory="public"), name="dashboard")

@app.get("/dashboard")
def get_dashboard():
    return FileResponse(os.path.join("public", "dashboard.html"))

# Request logging middleware (only logs errors and important info)
@app.middleware("http")
async def log_requests(request: Request, call_next):
    try:
        response = await call_next(request)
        # Only log errors or important requests
        if response.status_code >= 400:
            logger.error(f"❌ {request.method} {request.url} - Status: {response.status_code}")
        elif request.url.path == "/predict":
            logger.info(f"✅ Prediction request successful")
        return response
    except Exception as e:
        logger.error(f"❌ Request failed: {request.method} {request.url} - Error: {str(e)}")
        raise

# Allow CORS for frontend and Node.js backend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"]
)

# Logger setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("uvicorn")

# Health check
@app.get("/")
def root():
    return {"message": "FastAPI is running"}

# Pydantic model for structured API request
class PredictionInput(BaseModel):
    pclass: int
    sex: str
    age: float
    sibsp: int
    parch: int

@app.post("/predict")
def predict(input: PredictionInput):
    try:
        logger.info(f"Received input: {input}")
        result = predict_survival(input.pclass, input.sex, input.age, input.sibsp, input.parch)
        return {
            "prediction": result['prediction'],
            "confidence": result['confidence']
        }
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Prediction failed")
"""
@app.get("/")
def read_root():
    return {"message": "FastAPI Titanic Prediction API is running"}

@app.get("/api/fastapi")
def fastapi_status():
    return {
        "message": "FastAPI backend is connected",
        "status": "connected",
        "url": "http://localhost:5000",
        "services": {
            "uvicorn": "running",
            "ml_model": "loaded"
        },
        "endpoints": [
            "GET / - API status",
            "POST /predict - Make predictions",
            "GET /help - API help"
        ]
    }

@app.get("/help")
def help_info():
    return {
        "message": "API Help & Connection Information",
        "fastapi": {
            "status": "FastAPI is running",
            "url": "http://localhost:5000",
            "endpoints": {
                "predict": "POST /predict - Make Titanic survival predictions",
                "help": "GET /help - Show this help information"
            }
        },
        "nodejs": {
            "status": "Node.js backend is running",
            "url": "http://localhost:3000",
            "endpoints": {
                "predict_proxy": "POST /api/predict - Proxy to FastAPI prediction",
                "register": "POST /api/register - User registration",
                "login": "POST /api/login - User authentication"
            }
        },
        "usage": "Send POST request to /predict with: {pclass, sex, age, sibsp, parch}"
    }


@app.post("/predict")
def predict(input: PredictionInput):
    try:
        result = predict_survival(input.pclass, input.sex, input.age, input.sibsp, input.parch)
        return {
            "prediction": result["prediction"],
            "confidence": result["confidence"]
        }
    except Exception as e:
        logger.error(f"❌ Prediction failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Prediction failed")

"""
#again dashboard
# Load model and encoder
model = joblib.load("model/model.pkl")
encoder = joblib.load("model/encoder.pkl")

# Preprocess & evaluate once on startup
df_features = pd.read_csv("model/test.csv")
df_labels = pd.read_csv("model/gender_submission.csv")
df_test = pd.merge(df_features, df_labels, on="PassengerId")
X_test = df_test[["Pclass", "Sex", "Age", "SibSp", "Parch"]].copy()
X_test["Sex"] = encoder.transform(X_test["Sex"])
X_test.fillna(0, inplace=True)
y_true = df_test["Survived"].values
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_true, y_pred) * 100

@app.get("/api/stats")
def get_stats():
    survived = int((y_pred == 1).sum())
    died = int((y_pred == 0).sum())
    
    survivors_df = df_test[y_pred == 1]
    age_bins = pd.cut(survivors_df["Age"], bins=[0,10,20,30,40,50,60,70,80], right=False)
    age_counts = age_bins.value_counts().sort_index()

    return JSONResponse({
        "accuracy": f"{accuracy:.2f}",
        "total": len(y_pred),
        "survived": survived,
        "died": died,
        "ageBins": [f"{interval.left}-{interval.right}" for interval in age_counts.index],
        "ageCounts": age_counts.tolist()
    })

@app.get("/api/predictions")
def get_prediction_table():
    table = []
    for i in range(len(df_test)):
        table.append({
            "PassengerId": int(df_test.iloc[i]["PassengerId"]),
            "Pclass": int(df_test.iloc[i]["Pclass"]),
            "Age": float(df_test.iloc[i]["Age"]),
            "Actual": int(y_true[i]),
            "Predicted": int(y_pred[i])
        })
    return JSONResponse(table)

# Optional: Run directly
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=5000, reload=True)
