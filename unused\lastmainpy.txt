from fastapi import Fast<PERSON><PERSON>, Form
from fastapi.middleware.cors import CORSMiddleware
from model.ml_model import predict_survival

app = FastAPI()

# Enable CORS (allow frontend or Node.js backend to call this API)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "FastAPI Titanic Prediction API is running"}

@app.post("/predict")
def predict(pclass: int = Form(...), sex: str = Form(...), age: float = Form(...)):
    result = predict_survival(pclass, sex, age)
    return {
        "prediction": result["prediction"],
        "confidence": result["confidence"]
    }
