<!-- ✅ FILE: public/dashboard.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard Analytics</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body { font-family: Arial, sans-serif; background: #121212; color: #fff; margin: 0; padding: 0; }
    header { background: #1e1e2f; padding: 20px; text-align: center; font-size: 1.5em; }
    .dashboard-container { display: flex; flex-wrap: wrap; padding: 20px; gap: 20px; }
    .card { background: #1e1e2f; border-radius: 12px; padding: 20px; flex: 1; min-width: 300px; box-shadow: 0 0 10px #0099ff44; }
    canvas { background: #fff; border-radius: 8px; }
    table { width: 100%; border-collapse: collapse; margin-top: 10px; background: #2c2c3c; }
    th, td { padding: 10px; border: 1px solid #444; text-align: left; }
    th { background: #333; }
  </style>
</head>
<body>
  <header>
    Titanic ML Dashboard
  </header>

  <div class="dashboard-container">
    <div class="card">
      <h3>Total Predictions</h3>
      <p id="total-predictions">Loading...</p>
    </div>

    <div class="card">
      <h3>Survival Distribution</h3>
      <canvas id="survivalChart"></canvas>
    </div>

    <div class="card">
      <h3>Age Distribution of Survivors</h3>
      <canvas id="ageChart"></canvas>
    </div>

    <div class="card">
      <h3>User Prediction History</h3>
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Class</th>
            <th>Age</th>
            <th>Prediction</th>
            <th>Confidence</th>
          </tr>
        </thead>
        <tbody id="history-table">
          <tr><td colspan="5">Loading...</td></tr>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    async function fetchDashboardData() {
      const token = localStorage.getItem('token');
      const headers = { 'Authorization': `Bearer ${token}` };

     // const [metaRes, historyRes] = await Promise.all([
       // fetch('/api/user/stats', { headers }),
      //  fetch('/api/user/history', { headers })
     // ]);

     // const meta = await metaRes.json();
      //const history = await historyRes.json();

      const [metaRes, tableRes] = await Promise.all([
  fetch('/api/stats'),
  fetch('/api/predictions')
]);

const meta = await metaRes.json();
const predictions = await tableRes.json();

// Update total, charts
      document.getElementById('total-predictions').innerText = meta.total || 0;

      new Chart(document.getElementById('survivalChart'), {
        type: 'pie',
        data: {
          labels: ['Survived', 'Died'],
          datasets: [{
            data: [meta.survived, meta.died],
            backgroundColor: ['#4caf50', '#f44336']
          }]
        }
      });

      new Chart(document.getElementById('ageChart'), {
        type: 'bar',
        data: {
          labels: meta.ageBins,
          datasets: [{
            label: 'Survivors',
            data: meta.ageCounts,
            backgroundColor: '#2196f3'
          }]
        }
      });

      const tbody = document.getElementById('history-table');
      tbody.innerHTML = '';
      history.forEach(entry => {
        const tr = document.createElement('tr');
       /* tr.innerHTML = `
          <td>${new Date(entry.date).toLocaleString()}</td>
          <td>${entry.pclass}</td>
          <td>${entry.age}</td>
          <td>${entry.prediction}</td>
          <td>${entry.confidence}%</td> 

          

        `;*/
        tr.innerHTML = `
    <td>${entry.PassengerId}</td>
    <td>${entry.Pclass}</td>
    <td>${entry.Age}</td>
    <td>${entry.Predicted == 1 ? "Survived" : "Died"}</td>
    <td>${entry.Predicted == entry.Actual ? "✅" : "❌"}</td>
  `;
        tbody.appendChild(tr);
      });
    }

    fetchDashboardData();
  </script>
</body>
</html>
