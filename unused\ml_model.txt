import joblib
import os

# Load model and label encoder
base = os.path.dirname(__file__)
model = joblib.load(os.path.join(base, "model.pkl"))
sex_encoder = joblib.load(os.path.join(base, "sex_encoder.pkl"))

def predict_survival(pclass: int, sex: str, age: float):
    sex_encoded = sex_encoder.transform([sex.lower()])[0]  # male=1, female=0
    features = [[pclass, sex_encoded, age]]
    proba = model.predict_proba(features)[0]
    label = "Survive" if proba[1] > 0.5 else "Not Survive"
    return {"prediction": label, "confidence": round(proba[1] * 100, 2)}
