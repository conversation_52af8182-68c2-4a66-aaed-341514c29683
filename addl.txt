py conn string
 <div
        id="backend-status"
        hx-get="/status"
        hx-trigger="load"
        hx-swap="outerHTML"
    >
        Connecting to backend...
    </div>

    py main
    @app.get("/status", response_class=HTMLResponse)
def status():
    return '<div id="backend-status">✅ Connected to FastAPI backend</div>'




adding name
from model.ml_model import predict_survival

@app.post("/predict")
def predict(name: str = Form(...), age: int = Form(...), gender: str = Form(...)):
    # Example features: [age, is_male]
    features = [int(age), 1 if gender.lower() == "male" else 0]
    result = predict_survival(features)
    return f"{name} is likely to {result['prediction']} ({result['probability']}% confidence)"
HTMLResponse

<h2>Survival Prediction</h2>
<form hx-post="/predict" hx-target="#result" hx-swap="innerHTML">
    Name: <input type="text" name="name"><br>
    Age: <input type="number" name="age"><br>
    Gender:
    <select name="gender">
        <option>Male</option>
        <option>Female</option>
    </select><br>
    <button type="submit">Check Survival</button>
</form>

<div id="result">Prediction will appear here.</div>



for pred logic 
def predict_survival(pclass: int, sex: str, age: float):
    sex_encoded = sex_encoder.transform([sex.lower()])[0]  # male=1, female=0
    features = [[pclass, sex_encoded, age]]
    proba = model.predict_proba(features)[0]
    label = "Survive" if proba[1] > 0.5 else "Not Survive"
    return {"prediction": label, "confidence": round(proba[1] * 100, 2)}


    can we use jinja 2
    and whats diff
    from fastapi import FastAPI, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from model.ml_model import predict_survival
import os

app = FastAPI()

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(BASE_DIR, "public"))
app.mount("/static", StaticFiles(directory=os.path.join(BASE_DIR, "static")), name="static")

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/predict", response_class=HTMLResponse)
async def predict(request: Request, pclass: int = Form(...), sex: str = Form(...), age: float = Form(...)):
    result = predict_survival(pclass, sex, age)
    return f"<div id='prediction-result'>Prediction: {result['prediction']} ({result['confidence']}% confidence)</div>"


signin included in this
<!-- ✅ FILE: client/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Titanic Survival Predictor</title>
  <style>
    body {
      background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
      color: #fff;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    .auth-section, .signup-section {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #444;
      border-radius: 8px;
      background-color: #111;
      box-shadow: 0 0 10px #0ff;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
    .auth-section h3, .signup-section h3 {
      color: #0ff;
      margin-bottom: 10px;
    }
    .auth-section input, .signup-section input {
      display: block;
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: none;
      border-radius: 4px;
    }
    .auth-section button, .signup-section button {
      width: 100%;
      padding: 10px;
      background-color: #0ff;
      color: #000;
      font-weight: bold;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .toggle-link {
      text-align: center;
      margin-top: 10px;
      color: #0ff;
      cursor: pointer;
    }
    .hidden { display: none; }
  </style>
</head>
<body>
  <header>
    <img src="https://via.placeholder.com/40" alt="Logo">
    <h1>Titanic AI Predictor</h1>
    <div></div>
  </header>
  <div class="page-container">
    <div class="sidebar" id="sidebar">
      <button onclick="toggleSidebar()">⇦</button>
    </div>
    <div class="main-container">
      <div class="ribbon">
        <div class="options">
          <button>Home</button>
          <button>About</button>
          <button>Help</button>
        </div>
        <button class="sign-in">Sign In</button>
      </div>

      <div class="content">
        <!-- 🔐 Auth Section -->
        <div id="login-box" class="auth-section">
          <h3>Login</h3>
          <input type="text" id="email" placeholder="Email">
          <input type="password" id="password" placeholder="Password">
          <button onclick="loginUser()">Login</button>
          <div id="auth-message"></div>
          <div class="toggle-link" onclick="toggleForm('signup')">Don't have an account? Sign up</div>
        </div>

        <!-- 🆕 Signup Section -->
        <div id="signup-box" class="signup-section hidden">
          <h3>Sign Up</h3>
          <input type="text" id="signup-email" placeholder="Email">
          <input type="number" id="signup-age" placeholder="Age">
          <input type="password" id="signup-password" placeholder="Password">
          <button onclick="signupUser()">Create Account</button>
          <div id="signup-message"></div>
          <div class="toggle-link" onclick="toggleForm('login')">Already have an account? Login</div>
        </div>

        <form id="predict-form" class="hidden">
          <label for="pclass">Passenger Class</label>
          <select id="pclass" name="pclass">
            <option value="1">1st Class</option>
            <option value="2">2nd Class</option>
            <option value="3">3rd Class</option>
          </select>

          <label for="sex">Sex</label>
          <select id="sex" name="sex">
            <option value="male">Male</option>
            <option value="female">Female</option>
          </select>

          <label for="age">Age</label>
          <input type="number" id="age" name="age" step="0.1" required>

          <label for="sibsp">Number of Siblings/Spouses Aboard</label>
          <input type="number" id="sibsp" name="sibsp" required>

          <label for="parch">Number of Parents/Children Aboard</label>
          <input type="number" id="parch" name="parch" required>

          <button class="submit-btn" type="submit">Predict</button>
        </form>
        <div id="result"></div>
      </div>
    </div>
  </div>

  <script>
    function toggleForm(type) {
      document.getElementById('login-box').classList.toggle('hidden', type === 'signup');
      document.getElementById('signup-box').classList.toggle('hidden', type === 'login');
    }

    async function loginUser() {
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const msg = document.getElementById('auth-message');
      msg.textContent = 'Logging in...';

      try {
        const res = await fetch('http://localhost:3000/api/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password })
        });
        const data = await res.json();
        if (data.token) {
          msg.textContent = '✅ Login successful';
          localStorage.setItem('token', data.token);
          document.getElementById('predict-form').classList.remove('hidden');
        } else {
          msg.textContent = '❌ ' + (data.message || 'Login failed');
        }
      } catch (e) {
        msg.textContent = '❌ Error connecting to server';
      }
    }

    async function signupUser() {
      const email = document.getElementById('signup-email').value;
      const password = document.getElementById('signup-password').value;
      const age = parseInt(document.getElementById('signup-age').value);
      const msg = document.getElementById('signup-message');
      msg.textContent = 'Creating account...';

      try {
        const res = await fetch('http://localhost:3000/api/signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password, age })
        });
        const data = await res.json();
        if (data.success) {
          msg.textContent = '✅ Signup successful. Please login.';
          toggleForm('login');
        } else {
          msg.textContent = '❌ ' + (data.message || 'Signup failed');
        }
      } catch (e) {
        msg.textContent = '❌ Error connecting to server';
      }
    }

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      sidebar.classList.toggle('minimized');
    }
  </script>
</body>
</html>
