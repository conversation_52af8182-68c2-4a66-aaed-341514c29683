// ✅ FILE: server/index.js
require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const http = require('http');
const socketIO = require('socket.io');
const path = require('path');
const fs = require('fs');
const multer = require('multer');


const app = express();
const server = http.createServer(app);
/*const io = socketIO(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});*/
const io = socketIO(server, {
  cors: { origin: '*' } // or your frontend URL
});

let usersonline = 0;

// Middleware
app.use(cors());
app.use(express.json());

// ✅ Serve uploaded avatars
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));


// MongoDB connection
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => console.log('✅ MongoDB connected'))
  .catch((err) => console.error('❌ MongoDB error:', err));

// User model
const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email:{ type: String, required: true, unique: true },
  age: { type: Number, required: true },
  password: { type: String, required: true },
  avatar: { type: String, default: "https://via.placeholder.com/40" }, // Default avatar,
   visits: { type: Number, default: 0 }
});

const User = mongoose.model('User', userSchema);

//data schema

const dataSchema = new mongoose.Schema({
  username: { type: String, required: true },
  pclass: { type: Number },
  gender: { type: Number },
  result:{type:String, required: true},
  Date: { type: Date, default: Date.now }

});
const Data = mongoose.model('Data', dataSchema);

//multer
// Multer setup for avatar upload
const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});
const upload = multer({ storage });
// Auth Middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  if (!token) return res.status(401).json({ message: 'Token missing' });

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ message: 'Invalid token' });
    req.user = user;
    next();
  });
};

//app.get('/', (req, res) => {
  //res.sendFile(path.join(__dirname, 'public', 'index.html'));
//});

// Routes
app.get('/info', (req, res) => {
  res.json({
    message: 'Node.js server is running',
    status: 'connected',
    services: {
      mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      fastapi: 'proxy available'
    }
  });
});

app.get('/api/node', (req, res) => {
  res.json({
    message: 'Node.js backend is connected',
    status: 'connected',
    url: 'http://localhost:3000',
    services: {
      mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
      express: 'running',
      socketio: 'active'
    },
    endpoints: [
      'POST /api/predict - Prediction proxy',
      'POST /api/register - User registration',
      'POST /api/login - User authentication'
    ]
  });
});
app.post('/api/signup',upload.single('avatar'), async (req, res) => {
  const { username,email,age, password,preAvatar } = req.body;
 /* const hashedPassword = await bcrypt.hash(password, 10);

  try {
    const newUser = new User({ username, password: hashedPassword });
    await newUser.save();
    res.status(201).json({ message: 'User registered successfully' });
  } catch (err) {
    res.status(400).json({ error: 'User already exists or error occurred' });
  }
});
*/
 try {
    const existing = await User.findOne({ username });
    if (existing) return res.json({ success: false, message: 'User already exists' });
    const hashedPassword = await bcrypt.hash(password, 10);

   // const avatar = req.file ? `/uploads/${req.file.filename}` : undefined;
    // Final avatar logic
    let avatar = "https://via.placeholder.com/40";
    if (req.file) {
      avatar = `/uploads/${req.file.filename}`;
    } else if (preAvatar) {
      avatar = preAvatar;
    }
    await new User({ username,email, password: hashedPassword, age,avatar }).save();
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Signup failed' });
  }
});

app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  try{
  const user = await User.findOne({ username });
  if (!user) return res.status(401).json({ error: 'User not found' });

  const valid = await bcrypt.compare(password, user.password);
  if (!valid) return res.status(401).json({ error: 'Invalid credentials' });

    user.visits += 1;
await user.save();
  const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
  res.json({ token,
    username: user.username,
    avatar: user.avatar
   });
  }
  catch (err) {
    res.status(500).json({ message: 'Login error' });
  }


});

//profile info
app.get('/api/profile/:username', authenticateToken, async (req, res) => {
  const { username } = req.params;
  try {
    const user = await User.findOne({ username });
    if (!user) return res.status(404).json({ message: 'User not found' });

    res.json({
      username: user.username,
      email: user.email,
      age: user.age,
      avatar: user.avatar,
      visits: user.visits,
      userId: user._id,
      token: req.headers['authorization'].split(' ')[1]
    });
  } catch (err) {
    res.status(500).json({ message: 'Error fetching profile' });
  }
});


//edit profile
app.put('/api/edit-profile', upload.single('avatar'), authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { age, password, preAvatar } = req.body;

    const updateData = {};
    if (age) updateData.age = age;
    if (password) updateData.password = await bcrypt.hash(password, 10);

    if (req.file) {
      updateData.avatar = `/uploads/${req.file.filename}`;
    } else if (preAvatar) {
      updateData.avatar = preAvatar;
    }

    await User.findByIdAndUpdate(userId, updateData);
    res.json({ success: true, message: 'Profile updated successfully' });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Failed to update profile' });
  }
});

// Socket.io
io.on('connection', (socket) => {
  usersonline++;
  io.emit('userCount', { count: usersonline });
  console.log('🟢 New client connected:', socket.id);
  console.log('🟢 Online users:', usersonline);

  socket.on('disconnect', () => {
    usersonline--;
    io.emit('userCount', usersonline);
    console.log('🔴 Client disconnected:', socket.id);
  });
});

// FastAPI ML Integration Proxy
app.post('/api/predict', async (req, res) => {
  const fetch = (await import('node-fetch')).default;
  try {
    const response = await fetch('http://localhost:5000/predict', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(req.body)
    });
    const result = await response.json();
    res.json(result);
  } catch (err) {
    console.error('Error connecting to FastAPI:', err);
    res.status(500).json({ error: 'Prediction failed' });
  }
});

server.listen(3000, () => {
  console.log('🚀 Server running on http://localhost:3000');
});
//const path = require('path');
//const { type } = require('os');

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));