import pickle
import pandas as pd
from sklearn.metrics import accuracy_score
import joblib
# === Step 1: Load model and encoder ===
model = joblib.load("model/model.pkl")

encoder = joblib.load("model/encoder.pkl")

# === Step 2: Load test features and labels ===
features_df = pd.read_csv("model/test.csv")  # Contains 'id' + features
labels_df = pd.read_csv("model/gender_submission.csv")      # Contains 'id' + actual 'target'


# === Step 3: Normalize column names
#features_df.columns = features_df.columns.str.lower()
#labels_df.columns = labels_df.columns.str.lower()

# === Step 3: Merge both on 'id' ===
test_df = pd.merge(features_df, labels_df, on='PassengerId')

# ==== Step 3: Separate features and labels ====
# Assumes 'target' is the name of the column with actual results
# === Prepare features ===
X_raw = test_df[['Pclass', 'Sex', 'Age', 'SibSp', 'Parch']].copy()

# Encode 'Sex' using the same encoder as in training
X_raw['Sex'] = encoder.transform(X_raw['Sex'])

# Handle missing values
X_raw.fillna(0, inplace=True)


y_true= test_df['Survived'].values             # Actual labels




# === Step 7: Predict
y_pred = model.predict(X_raw)
# === Step 5: Encode/preprocess test features ===
#X_test = X_raw.values  # or just use X_raw directly if your model accepts a DataFrame
# Drop unnecessary columns







# ==== Step 5: Compare and compute accuracy ====
accuracy = accuracy_score(y_true, y_pred) * 100  # in percentage
print(f"✅ Model Accuracy on Test Data: {accuracy:.2f}%")

# ==== Optional: Print first few predictions ====
result_df = pd.DataFrame({
     'PassengerId': test_df['PassengerId'],
    'Actual': y_true,
    'Predicted': y_pred
})
print("\n🔍 Sample Predictions:")
print(result_df.head())
