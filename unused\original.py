from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pymongo import MongoClient
from bson.objectid import ObjectId
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()
MONGO_URL = os.getenv( 'mongodb+srv://prateekgaurav2005:<EMAIL>/')

# MongoDB setup
client = MongoClient(MONGO_URL)
db = client["members_db"]
collection = db["members"]

# FastAPI setup
app = FastAPI()
templates = Jinja2Templates(directory="public")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Home page showing members
@app.get("/", response_class=HTMLResponse)
def show_members(request: Request):
    members = list(collection.find())
    for member in members:
        member["_id"] = str(member["_id"])
    return templates.TemplateResponse("index.html", {"request": request, "members": members})

# Handle member form
@app.post("/add")
def add_member(name: str = Form(...), age: int = Form(...)):
    collection.insert_one({"name": name, "age": age})
    return RedirectResponse("/", status_code=303)
 #earlier

