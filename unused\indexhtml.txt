<!-- ✅ FILE: client/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Titanic Survival Predictor</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #f4f4f4;
      padding: 20px;
      max-width: 600px;
      margin: auto;
    }
    h1 {
      text-align: center;
      color: #333;
    }
    form {
      display: flex;
      flex-direction: column;
      gap: 12px;
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    label {
      font-weight: bold;
    }
    select, input {
      padding: 8px;
      font-size: 16px;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px;
      font-size: 16px;
      border-radius: 5px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0056b3;
    }
    #result {
      margin-top: 20px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>Titanic Survival Predictor</h1>
  <form id="predict-form">
    <label for="pclass">Passenger Class</label>
    <select id="pclass" name="pclass">
      <option value="1">1st Class</option>
      <option value="2">2nd Class</option>
      <option value="3">3rd Class</option>
    </select>

    <label for="sex">Sex</label>
    <select id="sex" name="sex">
      <option value="male">Male</option>
      <option value="female">Female</option>
    </select>

    <label for="age">Age</label>
    <input type="number" id="age" name="age" step="0.1" required>

    <button type="submit">Predict</button>
  </form>

  <div id="result"></div>

  <script>
    const form = document.getElementById('predict-form');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);

      const response = await fetch('http://localhost:5000/predict', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.prediction) {
        resultDiv.textContent = `${data.prediction} with ${data.confidence}% confidence.`;
      } else {
        resultDiv.textContent = 'Prediction failed. Please try again.';
      }
    });
  </script>
</body>
</html>
