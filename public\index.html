
<!-- ✅ FILE: client/index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Titanic Survival Predictor</title>
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(-45deg, #0454a4, #015ee1, #023f8a, #024990);
      background-size: 400% 400%;
      animation: flowing-water 20s ease infinite;
      color: white;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    @keyframes flowing-water {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 30px;
      background-color: #00122b;
      box-shadow: 0 2px 5px rgba(0,0,0,0.4);
    }

    header h1 {
      margin: 0 auto;
      font-size: 26px;
    }

    header img {
      height: 40px;
    }

    .page-container {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .sidebar {
      width: 250px;
      background: rgba(0, 0, 50, 0.4);
      box-shadow: 0 0 20px rgba(0, 191, 255, 0.7);
      transition: width 0.3s;
      padding: 20px;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
      backdrop-filter: blur(8px);
    }

    .sidebar.minimized {
      width: 20px;
    }

    .main-container {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }

    .ribbon {
      display: flex;
      justify-content: space-between;
      background-color: rgba(255,255,255,0.1);
      padding: 10px 20px;
      align-items: center;
    }

    .ribbon .options {
      display: flex;
      gap: 15px;
    }

    .ribbon .options button,
    .ribbon .sign-in,
    button.submit-btn,.signoutBtn,
    .sidebar button {
      background: linear-gradient(45deg, #00aced, #0088cc, #00aced);
      background-size: 300% 300%;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 5px;
      cursor: pointer;
      transition: background-position 1s ease, box-shadow 0.3s;
    /* animation: pulse-glow 4s infinite ease-in-out; */
    }

    .ribbon .options button:hover,
    .ribbon .sign-in:hover,
    button.submit-btn:hover,.signoutBtn:hover,
    .sidebar button:hover {
      background-position: right center;
      box-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff;
    transform: scale(1.08);
    filter: brightness(1.2);
    }

    @keyframes pulse-glow {
      0% { box-shadow: 0 0 5px #00aced; }
      50% { box-shadow: 0 0 20px #00ffff; }
      100% { box-shadow: 0 0 5px #00aced; }
    }

    .content {
      padding: 20px;
      overflow-y: auto;
    }

    form {
      background-color: rgba(0,0,0,0.2);
      padding: 20px;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      max-width: 500px;
      margin: auto;
      gap: 12px;
    }

    label {
      font-weight: bold;
    }

    select, input {
      padding: 8px;
      font-size: 16px;
      border-radius: 5px;
      border: none;
    }

    #result {
      margin-top: 20px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }

    /* Help Button */
    .help-btn {
      background: linear-gradient(45deg, #28a745, #20c997);
      color: white;
      padding: 12px 24px;
      margin-left: 10px;
      cursor: pointer;
      font-size: 16px;
      border-radius: 5px;
      border: none;
      transition: all 0.3s ease;
    }

    .help-btn:hover {
      background: linear-gradient(45deg, #20c997, #28a745);
      transform: scale(1.05);
      box-shadow: 0 0 10px #28a745;
    }

     #online-users {
      margin: 10px auto;
      text-align: center;
      font-weight: bold;
      color: #0ff;
    }
    .avatar-choice {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: 0.2s;
}
.avatar-choice:hover {
  transform: scale(1.1);
}
.avatar-choice.selected {
  border-color: #007bff;
}
.hidden {
  display: none;
}
#signin.profile-mode {
  font-weight: bold;
  padding: 8px 16px;
}

    /* Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
      background-color: #1a1a2e;
      margin: 5% auto;
      padding: 20px;
      border-radius: 10px;
      width: 80%;
      max-width: 600px;
      color: white;
      border: 2px solid #00ffff;
    }

    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }

    .close:hover {
      color: #00ffff;
    }

    .help-info h3 {
      color: #00ffff;
      margin-top: 20px;
    }

    .help-info ul {
      margin-left: 20px;
    }

    .help-info li {
      margin: 5px 0;
    }
     .auth-section, .signup-section {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #444;
      border-radius: 8px;
      background-color: #111;
      box-shadow: 0 0 10px #0ff;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
     .close-btn {
      position: absolute;
      top: 10px;
      right: 15px;
      font-size: 20px;
      cursor: pointer;
      color: #999;
    }

    .close-btn:hover {
      color: red;
    }
    .auth-section h3, .signup-section h3 {
      color: #0ff;
      margin-bottom: 10px;
    }
    .auth-section input, .signup-section input {
      display: block;
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: none;
      border-radius: 4px;
    }
    .auth-section button, .signup-section button {
      width: 100%;
      padding: 10px;
      background-color: #0ff;
      color: #000;
      font-weight: bold;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .toggle-link {
      text-align: center;
      margin-top: 10px;
      color: #0ff;
      cursor: pointer;
    }
    .hidden { display: none; }
  </style>
</head>
<body>
  <header>
    <img src="https://media.istockphoto.com/id/503132519/photo/titanic-and-iceberg.jpg?s=612x612&w=0&k=20&c=cO71OMKsceiSj07heG1jhOiNRNGevD-XrACWis2RdQ4=" alt="Logo">
    <h1>Titanic AI Predictor</h1>
    <div></div>
  </header>
  <div class="page-container">
    <div class="sidebar" id="sidebar">
      <button onclick="toggleSidebar()">⇦</button>
      <!-- You can add sidebar content here -->
    </div>
    <div class="main-container">
      <div class="ribbon">
        <div class="options">
          <button>Home</button>
          <button onclick="window.location.href='about.html'">About</button>
          <button onclick="showHelp()">Help</button>
        </div>
        <button id="signin" class="sign-in" >Sign In</button>
        <button id="signoutBtn" class="hidden">Sign Out</button>
      </div>

 <div class="content">
  <div id="online-users">🟢 Online Users: unknown</div>
        <!-- 🔐 Auth Section -->
         <!-- 🆕 Login Section -->
 <div id="authPopup" class="modal">

        <div id="login-box" class="auth-section">

          <h3>Login</h3>
          <input type="text" id="username" placeholder="Username">
          <input type="password" id="password" placeholder="Password">
          <button onclick="loginUser()">Login</button>
          <div id="auth-message"></div>
          <div class="toggle-link" onclick="toggleForm('signup')">Don't have an account? Sign up</div>
        
         
      <!-- Step 3: Close icon (X) -->
      <span class="close-btn" id="closePopup">&times;</span>


        </div>

 </div>

        <!-- 🆕 Signup Section -->
        <div id="signup-box" class="signup-section hidden">
          <h3>Sign Up</h3>
          <input type="text" id="signup-email" placeholder="Email*">
                    <input type="text" id="signup-username" placeholder="Username*">

          <input type="number" id="signup-age" placeholder="Age">
          <input type="password" id="signup-password" placeholder="Password*">
          <input type="file" name="avatar" accept="image/*">
          <label>Or Choose Avatar:</label>
<div id="preloaded-avatars" style="display: flex; gap: 10px; flex-wrap: wrap;">
  <img src="https://i.redd.it/n10h2fdlpb291.jpg" class="avatar-choice" onclick="selectPreloadedAvatar(this)">
  <img src="data:image/jpeg;base64,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" class="avatar-choice" onclick="selectPreloadedAvatar(this)">
  <img src="https://hips.hearstapps.com/hmg-prod/images/Emma-Watson_GettyImages-*********.jpg?crop=1xw:1.0xh;center,top&resize=640:*" class="avatar-choice" onclick="selectPreloadedAvatar(this)">
  <img src="https://i.pinimg.com/736x/03/80/c1/0380c1cc7fb3be41ae880e9442492e61.jpg" class="avatar-choice" onclick="selectPreloadedAvatar(this)">
</div>
<input type="hidden" id="preloaded-avatar-url" name="preAvatar">
          <button onclick="signupUser()">Create Account</button>
          <div id="signup-message"></div>
          <div class="toggle-link" onclick="toggleForm('login')">Already have an account? Login</div>
        </div>


        <!--profile-->
        <div id="profileModal" class="modal">
  <div class="modal-content" style="width: 400px;">
    <span class="close" onclick="closeProfile()">&times;</span>
    <h2>User Profile</h2>
    <div id="profile-info">
      <!-- Filled dynamically -->
    </div>
    <div style="margin-top: 20px; text-align: center;">
      <button onclick="enableEdit()">Edit Details</button>
      <button onclick="saveProfileChanges()">Save Changes</button>
      <button onclick="logoutUser()">Sign Out</button>
    </div>
  </div>
</div>
        <!-- 🛳️ Prediction Form -->
        <form id="predict-form">
          <label for="pclass">Passenger Class</label>
          <select id="pclass" name="pclass">
            <option value="1">1st Class</option>
            <option value="2">2nd Class</option>
            <option value="3">3rd Class</option>
          </select>

          <label for="sex">Sex</label>
          <select id="sex" name="sex">
            <option value="male">Male</option>
            <option value="female">Female</option>
          </select>

          <label for="age">Age</label>
          <input type="number" id="age" name="age" step="1" required>
          
          <label for="sibsp">Number of Siblings/Spouses Aboard</label>
          <input type="number" id="sibsp" name="sibsp" required>

          <label for="parch">Number of Parents/Children Aboard</label>
          <input type="number" id="parch" name="parch" required>
          
          <button class="submit-btn" type="submit">Predict</button>
        </form>
        <div id="result"></div>


 <div style="margin-top: 20px; text-align: center;">
          <button onclick="checkAccuracy()" class="submit-btn">Check Model Accuracy</button>
          <div id="accuracy-message" style="margin-top: 10px; font-weight: bold;"></div>
        
        
          <!-- Help Modal -->
        <div id="helpModal" class="modal">
          <div class="modal-content">
            <span class="close" onclick="closeHelp()">&times;</span>
            <h2>🔗 Connection Information</h2>
            <div class="help-info">
              <div id="connectionStatus">
                <p>🔄 Checking connections...</p>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
<script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

  <script>
 const socket = io('http://localhost:3000');
    //const userDisplay = document.getElementById('online-users');
   // socket.on('userCount', count => {
     // userDisplay.textContent = `🟢 Online Users: ${count}`;
      socket.on('userCount', (data) => {
    document.getElementById('online-users').textContent =
      `🟢 Online Users: ${data.count}`;
    });



const signbtn = document.getElementById('signin');
    const popupOverlay = document.getElementById('authPopup');
    const closeBtn = document.getElementById('closePopup');

    // Step 6: Show popup when button is clicked
    signbtn.addEventListener('click', () => {
      popupOverlay.style.display = 'block';
    });

    // Step 7: Hide popup when close (X) is clicked
    closeBtn.addEventListener('click', () => {
      popupOverlay.style.display = 'none';
    });

    // Step 8: Hide popup if user clicks outside the box
    window.addEventListener('click', (event) => {
      if (event.target === popupOverlay) {
        popupOverlay.style.display = 'none';
      }
    });

// ✅ Avatar selector
function selectPreloadedAvatar(img) {
  document.querySelectorAll('.avatar-choice').forEach(a => a.classList.remove('selected'));
  img.classList.add('selected');
  document.getElementById('preloaded-avatar-url').value = img.src;
}
//login signup functions
function toggleForm(type) {
      document.getElementById('login-box').classList.toggle('hidden', type === 'signup');
      document.getElementById('signup-box').classList.toggle('hidden', type === 'login');
    }

    async function loginUser() {
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const msg = document.getElementById('auth-message');
      msg.textContent = 'Logging in...';

      try {
        const res = await fetch('http://localhost:3000/api/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, password })
        });
        const data = await res.json();
        if (data.token) {
          msg.textContent = '✅ Login successful';
          localStorage.setItem('token', data.token);
          document.getElementById('predict-form').classList.remove('hidden');
          
localStorage.setItem('user', JSON.stringify({
  token: data.token,
  username: data.username,
  avatar: data.avatar
}));
showLoggedInUI({ token: data.token, username: data.username, avatar: data.avatar });


        } 
        
        else {
          msg.textContent = '❌ ' + (data.message || 'Login failed');
        }
      } catch (e) {
        msg.textContent = '❌ Error connecting to server';
      }
    }

    async function signupUser() {
      const email = document.getElementById('signup-email').value;
            const username = document.getElementById('signup-username').value;

      const password = document.getElementById('signup-password').value;
      const age = parseInt(document.getElementById('signup-age').value);
        const avatarFile = document.querySelector('input[name="avatar"]').files[0];
const selectedAvatar = document.getElementById('preloaded-avatar-url').value;
      const msg = document.getElementById('signup-message');
      msg.textContent = 'Creating account...';

// Email validation
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    msg.textContent = '❌ Please enter a valid email address.';
    return;
  }

      const formData = new FormData();
  formData.append('email', email);
  formData.append('username', username);
  formData.append('password', password);
  formData.append('age', age);
  if (avatarFile) {
    formData.append('avatar', avatarFile); // this matches multer.single('avatar')
  }
 else if (selectedAvatar) formData.append('preAvatar', selectedAvatar);

      try {
        const res = await fetch('http://localhost:3000/api/signup', {
          method: 'POST',
                body: formData // no content-type header needed

         // headers: { 'Content-Type': 'application/json' },
          //body: JSON.stringify({ email,username, password, age ,avatar})
        });
        const data = await res.json();
        if (data.success) {
          msg.textContent = '✅ Signup successful. Please login.';
          toggleForm('login');
        } else {
          msg.textContent = '❌ ' + (data.message || 'Signup failed');
        }
      } catch (e) {
        msg.textContent = '❌ Error connecting to server';
      }
    }

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      sidebar.classList.toggle('minimized');}

// ✅ Run this after DOM loads
window.onload = () => {
  const user = JSON.parse(localStorage.getItem('user'));
  if (user && user.token) {
    showLoggedInUI(user);
  }
};

function showLoggedInUI(user) {
  const signInBtn = document.querySelector('.sign-in');
   const signOutBtn = document.getElementById('signoutBtn');
  signInBtn.innerHTML = `
    <img src="${user.avatar}" alt="Profile" style="width: 32px; height: 32px; border-radius: 50%; vertical-align: middle;">
    <span style="margin-left: 8px;">${user.username}</span>
  `;
  signInBtn.onclick = () => {
    document.getElementById('profileModal').style.display = 'block';
    showProfile(user);
  };
signInBtn.classList.add('profile-mode');

  // Show Sign Out button
  signOutBtn.classList.remove('hidden');

  // Optional: open profile dialog on click
 // signInBtn.onclick = () => openProfileDialog(user);

}
document.getElementById('signoutBtn').addEventListener('click', () => {
  localStorage.clear();

  const signInBtn = document.getElementById('signin');
  signInBtn.textContent = 'Sign In';
  signInBtn.classList.remove('profile-mode');
  signInBtn.onclick = () => {
    document.getElementById('authPopup').style.display = 'block';
  };

  document.getElementById('signoutBtn').classList.add('hidden');
  location.reload(); // optional
});


//barrier
 /*
const user = JSON.parse(localStorage.getItem('user'));
if (!user || !user.token) {
  alert('🔒 Please sign in to access this feature.');
  return;
}

// Include token in request:
const response = await fetch('/api/predict', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${user.token}`
  },
  body: JSON.stringify(formData)
});
*/

//ml

    const form = document.getElementById('predict-form');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = {
        pclass: parseInt(document.getElementById('pclass').value),
        sex: document.getElementById('sex').value,
        age: parseFloat(document.getElementById('age').value),
        sibsp: parseInt(document.getElementById('sibsp').value),
        parch: parseInt(document.getElementById('parch').value),
      };

 const response = await fetch('http://localhost:3000/api/predict', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify(formData)
     });

      const data = await response.json();

      if (data.prediction) {
        resultDiv.textContent = `${data.prediction} with ${data.confidence}% confidence.`;
      } else {
        resultDiv.textContent = 'Prediction failed. Please try again.';
      }
    });

    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      sidebar.classList.toggle('minimized');
    }

    async function showHelp() {
      document.getElementById('helpModal').style.display = 'block';
      await checkConnections();
    }

    async function fetchMessageFastAPI() {
      try {
        const res = await fetch('http://localhost:5000/api/fastapi');
        const data = await res.json();
        return {
          connected: true,
          data: data
        };
      } catch (error) {
        return {
          connected: false,
          error: error.message
        };
      }
    }

    async function fetchMessageNode() {
      try {
        const res = await fetch('http://localhost:3000/api/node');
        const data = await res.json();
        return {
          connected: true,
          data: data
        };
      } catch (error) {
        return {
          connected: false,
          error: error.message
        };
      }
    }

    async function checkConnections() {
      const statusDiv = document.getElementById('connectionStatus');
      statusDiv.innerHTML = '<p>🔄 Checking connections...</p>';

      let html = '';

      // Check FastAPI
      const fastApiResult = await fetchMessageFastAPI();
      if (fastApiResult.connected) {
        const data = fastApiResult.data;
        html += `
          <h3>✅ FastAPI Backend</h3>
          <p><strong>Status:</strong> <span style="color: #28a745;">Connected</span></p>
          <p><strong>Message:</strong> ${data.message}</p>
          <p><strong>URL:</strong> ${data.url}</p>
          <p><strong>Services:</strong> ML Model: ${data.services.ml_model}, Uvicorn: ${data.services.uvicorn}</p>
          <p><strong>Endpoints:</strong></p>
          <ul>
            ${data.endpoints.map(endpoint => `<li>${endpoint}</li>`).join('')}
          </ul>
        `;
      } else {
        html += `
          <h3>❌ FastAPI Backend</h3>
          <p><strong>Status:</strong> <span style="color: #dc3545;">Not Connected</span></p>
          <p><strong>URL:</strong> http://localhost:5000</p>
          <p><strong>Error:</strong> ${fastApiResult.error}</p>
        `;
      }

      // Check Node.js
      const nodeResult = await fetchMessageNode();
      if (nodeResult.connected) {
        const data = nodeResult.data;
        const mongoStatus = data.services.mongodb;
        html += `
          <h3>✅ Node.js Backend</h3>
          <p><strong>Status:</strong> <span style="color: #28a745;">Connected</span></p>
          <p><strong>Message:</strong> ${data.message}</p>
          <p><strong>URL:</strong> ${data.url}</p>
          <p><strong>Services:</strong> MongoDB: <span style="color: ${mongoStatus === 'connected' ? '#28a745' : '#ffc107'}">${mongoStatus}</span>, Express: ${data.services.express}, Socket.IO: ${data.services.socketio}</p>
          <p><strong>Endpoints:</strong></p>
          <ul>
            ${data.endpoints.map(endpoint => `<li>${endpoint}</li>`).join('')}
          </ul>
        `;
      } else {
        html += `
          <h3>❌ Node.js Backend</h3>
          <p><strong>Status:</strong> <span style="color: #dc3545;">Not Connected</span></p>
          <p><strong>URL:</strong> http://localhost:3000</p>
          <p><strong>Error:</strong> ${nodeResult.error}</p>
        `;
      }

      // Add usage info if at least one service is connected
      if (html.includes('Connected')) {
        html += `
          <h3>📋 Usage</h3>
          <p>Send POST request to /predict with: {pclass, sex, age, sibsp, parch}</p>
        `;
      }

      statusDiv.innerHTML = html;
    }

    function closeHelp() {
      document.getElementById('helpModal').style.display = 'none';
    }

    // Close modal when clicking outside of it
    window.onclick = function(event) {
      const modal = document.getElementById('helpModal');
      if (event.target == modal) {
        modal.style.display = 'none';
      }
    }
    function showProfilePopup() {
  const user = JSON.parse(localStorage.getItem('user'));
  const infoDiv = document.getElementById('profile-info');
  infoDiv.innerHTML = `
    <img src="${user.avatar}" style="width: 80px; height: 80px; border-radius: 50%;" />
    <p><strong>Username:</strong> ${user.username}</p>
    <p><strong>User ID:</strong> ${user.userId || 'hidden'}</p>
    <p><strong>Email:</strong> ${user.email || 'hidden'}</p>
    <p><strong>Age:</strong> <input id="edit-age" type="number" value="${user.age || ''}" disabled /></p>
    <p><strong>Total Visits:</strong> ${user.visits || 1}</p>
    <p><strong>Token:</strong> <code style="font-size: 10px">${user.token}</code></p>

    <label>New Avatar (optional):</label><input type="file" id="edit-avatar" accept="image/*"><br>
    <label>New Password:</label><input type="password" id="edit-password" placeholder="Leave blank to keep current">
  `;
  document.getElementById('profileModal').style.display = 'block';
}

function closeProfile() {
  document.getElementById('profileModal').style.display = 'none';
}

function enableEdit() {
  document.getElementById('edit-age').disabled = false;
}

function saveProfileChanges() {
  const age = document.getElementById('edit-age').value;
  const avatar = document.getElementById('edit-avatar').files[0];
  const password = document.getElementById('edit-password').value;

  const formData = new FormData();
  if (age) formData.append('age', age);
  if (avatar) formData.append('avatar', avatar);
  if (password) formData.append('password', password);

  fetch('http://localhost:3000/api/edit-profile', {
    method: 'PUT',
    headers: { Authorization: 'Bearer ' + JSON.parse(localStorage.getItem('user')).token },
    body: formData
  }).then(res => res.json())
    .then(data => {
      if (data.success) {
        alert('✅ Profile updated. Please reload.');
        location.reload();
      } else {
        alert('❌ Failed to update profile');
      }
    });
}

function logoutUser() {
  localStorage.clear();
  location.reload();
}

  </script>
</body>
</html>
