import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
import joblib

# Load dataset
df = pd.read_csv("c:/Users/<USER>/OneDrive/Documents/web/model/train.csv")

# Select relevant columns
df = df[["Survived", "Pclass", "Sex", "Age"]]

# Drop rows with missing Age
df = df.dropna()

# Encode 'Sex'
le = LabelEncoder()
df["Sex"] = le.fit_transform(df["Sex"])  # male=1, female=0

# Features and target
X = df[["Pclass", "Sex", "Age"]]
y = df["Survived"]

# Train model
model = RandomForestClassifier()
model.fit(X, y)

# Save model and encoder
joblib.dump(model, "model.pkl")
joblib.dump(le, "sex_encoder.pkl")

print("✅ Model and encoder saved!")
